package com.near_reality.cache_tool

import com.near_reality.cache.file
import com.near_reality.cache.group
import mgi.tools.jagcached.ArchiveType
import org.runestar.cs2.SCRIPT_NAMES
import org.runestar.cs2.cg.StrictGenerator
import org.runestar.cs2.decompile
import org.runestar.cs2.type.Script
import org.runestar.cs2.util.*
import java.nio.file.Files
import java.nio.file.Path
import java.util.*
import kotlin.io.path.exists
import kotlin.io.path.readBytes

private const val CS2_COMPILE_EXTENSION = ".cs2" // May also be .cs2.pack
private const val SCRIPT_TO_DECOMPILE = "10587" // Change this to cs2 id

private val userHome = System.getProperty("user.home")
private val baseAssetsPath = "$userHome/Desktop/Exiles/Exiles/cache/assets"

fun main() {
    cacheTo
    decompileScript()
    //decompileStore() //Also need to comment out the SCRIPT_TO_DECOMPILE line in decompileCs2
    //decompileQuestTab() //Also need to comment out the SCRIPT_TO_DECOMPILE line in decompileCs2
}

private fun decompileStore() {
    val readme = StringBuilder()
    val baseDir = Path.of("$baseAssetsPath/osnr/store_interface")
    val loadDir = Path.of("$baseAssetsPath/osnr/store_interface/cs2")
    val saveDir = Path.of("$baseAssetsPath/osnr/store_interface/cs2_decompiled")

    decompileCs2(saveDir, readme, loadDir)
}

private fun decompileQuestTab() {
    val readme = StringBuilder()
    val loadDir = Path.of("$baseAssetsPath/cs2/quest_tab")
    val saveDir = Path.of("$baseAssetsPath/cs2/quest_tab_decompiled")

    decompileCs2(saveDir, readme, loadDir)
}

private fun decompileScript() {
    val readme = StringBuilder()
    val loadDir = Path.of("$baseAssetsPath/osnr/custom_cs2")
    val saveDir = Path.of("$baseAssetsPath/osnr/custom_cs2_decompiled")

    decompileCs2(saveDir, readme, loadDir)
}

private fun decompileCs2(
    saveDir: Path,
    readme: StringBuilder,
    loadDir: Path,
    cs2Extension: String = CS2_COMPILE_EXTENSION
) {
    Files.createDirectories(saveDir)

    val generator = StrictGenerator { scriptId, scriptName, script ->
        Files.writeString(saveDir.resolve("$scriptName.cs2"), script)
        if (SCRIPT_NAMES.load(scriptId) != null) {
            readme.append("[**$scriptId**](scripts/$scriptName.cs2) `$scriptName`  \n")
        } else {
            readme.append("[**$scriptId**](scripts/$scriptName.cs2)  \n")
        }
    }

    val scriptLoader = Loader {
        val scriptFileName = it.toString() + cs2Extension
        val scriptFile = loadDir.resolve(scriptFileName)
        if (scriptFile.exists()) {
            val scriptBytes = scriptFile.readBytes()
            println("Loading $scriptFile")
            Script(scriptBytes)
        } else null
    }.orElse {
        println("Loading from cache $it")
        val scriptData = cacheTo.getArchive(ArchiveType.CLIENTSCRIPTS).group(it).file(0).data.run {
            position = 0
            readFullBytes()
        }
        Script(scriptData)
    }.caching()

    val scriptIds = loadDir
        .list()
        .filter { it.endsWith(cs2Extension) }
        .filter { it.startsWith(SCRIPT_TO_DECOMPILE) } // Comment out to decompile all
        .mapTo(TreeSet()) {
            it.substringBefore(cs2Extension).toInt()
        }

    decompile(scriptLoader.withIds(scriptIds), generator)

    println(readme)
    Files.writeString(saveDir.resolve("README.md"), readme)
}
